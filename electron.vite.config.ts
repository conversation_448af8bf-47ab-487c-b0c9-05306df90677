import { resolve } from 'path'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  main: {
    plugins: [externalizeDepsPlugin()]
  },
  preload: {
    plugins: [externalizeDepsPlugin({
      exclude: ['@electron-toolkit/preload']
    })],
    build: {
      rollupOptions: {
        external: []
      }
    }
  },
  renderer: {
    resolve: {
      alias: {
        '@renderer': resolve('src/renderer/src'),
        '@': resolve('src/renderer/src'),
        '@/ui': resolve('src/renderer/src/components/ui'),
        '@/components': resolve('src/renderer/src/components'),
        '@/types': resolve('src/renderer/types'),
        '@/stores': resolve('src/renderer/src/stores'),
        '@/contexts': resolve('src/renderer/src/contexts'),
        '@/hooks': resolve('src/renderer/src/hooks'),
        '@/lib': resolve('src/renderer/src/lib'),
        '@/services': resolve('src/renderer/src/services'),
        'src/renderer/src': resolve('src/renderer/src')
      }
    },
    plugins: [react()]
  }
})
