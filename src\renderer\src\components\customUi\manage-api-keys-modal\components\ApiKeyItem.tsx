import React, { useState, useRef } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { GripVerticalIcon, TrashIcon, CheckIcon, XIcon } from 'lucide-react';
import { Button } from '../../../ui/button';
import { Input } from '../../../ui/input';
import { Switch } from '../../../ui/switch';
import { Toolt<PERSON>, TooltipContent, TooltipTrigger, TooltipProvider } from '../../../ui/tooltip';
import { cn } from '../../../../lib/utils';
import { ApiKeyItemProps, DND_ITEM_TYPE_API_KEY } from '../types';
import { useNotification } from '../../../../contexts/NotificationContext';

export const ApiKeyItem: React.FC<ApiKeyItemProps> = ({
  config,
  index,
  moveConfig,
  handleVisibilityToggle,
  handleInitiateDelete,
  handleUpdateConfigField
}) => {
  const { showNotification } = useNotification();

  // Editing states
  const [isEditingName, setIsEditingName] = useState(false);
  const [isEditingApiKey, setIsEditingApiKey] = useState(false);
  const [isEditingApiUrl, setIsEditingApiUrl] = useState(false);

  // Current values
  const [currentNameValue, setCurrentNameValue] = useState(config.name);
  const [currentApiKeyValue, setCurrentApiKeyValue] = useState(config.apiKey);
  const [currentApiUrlValue, setCurrentApiUrlValue] = useState(config.apiUrl);

  // Refs for drag and drop
  const itemRef = useRef<HTMLDivElement>(null);
  const handleRef = useRef<HTMLDivElement>(null);

  // Drag and drop setup
  const [, drop] = useDrop({
    accept: DND_ITEM_TYPE_API_KEY,
    hover: (item: { id: string; index: number }, monitor) => {
      if (!itemRef.current) return;
      const dragIndex = item.index;
      const hoverIndex = index;
      if (dragIndex === hoverIndex) return;

      const hoverBoundingRect = itemRef.current.getBoundingClientRect();
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const clientOffset = monitor.getClientOffset();
      if (!clientOffset) return;
      const hoverClientY = clientOffset.y - hoverBoundingRect.top;
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) return;
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) return;
      moveConfig(dragIndex, hoverIndex).catch(console.error);
      item.index = hoverIndex;
    },
  });

  const [{ isDragging }, drag, dragPreview] = useDrag({
    type: DND_ITEM_TYPE_API_KEY,
    item: { id: config.id, index },
    collect: (monitor) => ({ isDragging: monitor.isDragging() }),
  });

  drag(handleRef);
  drop(itemRef);
  dragPreview(itemRef);

  // Save functions
  const onSaveName = async () => {
    const newNameTrimmed = currentNameValue.trim();
    if (newNameTrimmed === '') {
      showNotification("Configuration name cannot be empty.", "error");
      setCurrentNameValue(config.name);
      setIsEditingName(false);
      return;
    }
    if (newNameTrimmed !== config.name) {
      await handleUpdateConfigField(config, 'name', newNameTrimmed);
    }
    setIsEditingName(false);
  };

  const onCancelNameEdit = () => {
    setCurrentNameValue(config.name);
    setIsEditingName(false);
  };

  const onSaveApiKey = async () => {
    if (currentApiKeyValue !== config.apiKey) {
      await handleUpdateConfigField(config, 'apiKey', currentApiKeyValue);
    }
    setIsEditingApiKey(false);
  };

  const onCancelApiKeyEdit = () => {
    setCurrentApiKeyValue(config.apiKey);
    setIsEditingApiKey(false);
  };

  const onSaveApiUrl = async () => {
    if (currentApiUrlValue !== config.apiUrl) {
      await handleUpdateConfigField(config, 'apiUrl', currentApiUrlValue);
    }
    setIsEditingApiUrl(false);
  };

  const onCancelApiUrlEdit = () => {
    setCurrentApiUrlValue(config.apiUrl);
    setIsEditingApiUrl(false);
  };

  return (
    <div
      ref={itemRef}
      style={{ opacity: isDragging ? 0.5 : 1 }}
      className={cn(
        "max-w-[97%] p-3 border border-border rounded-md flex flex-col group mb-4 relative bg-secondary dark:bg-secondary",
      )}
    >
      {/* Header with name and actions */}
      <div className="flex items-center justify-between mb-3 gap-2">
        <div className="flex items-center flex-grow min-w-0 gap-1">
          {/* Drag handle */}
          <div
            ref={handleRef}
            className="cursor-grab touch-none flex items-center mr-1"
          >
            <GripVerticalIcon className="h-4 w-4 text-muted-foreground" />
          </div>

          {isEditingName ? (
            <div className="flex-grow flex items-center gap-1">
              <Input
                value={currentNameValue}
                onChange={(e) => setCurrentNameValue(e.target.value)}
                className="h-8 text-sm flex-grow dark:bg-input text-text-foreground"
                autoFocus
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    onSaveName();
                  } else if (e.key === 'Escape') {
                    onCancelNameEdit();
                  }
                }}
              />
              <TooltipProvider delayDuration={300}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" onClick={onSaveName} className="h-7 w-7 group/save">
                      <CheckIcon className="h-4 w-4 text-primary group-hover/save:text-primary-foreground" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom"><p>Save name</p></TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider delayDuration={300}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" onClick={onCancelNameEdit} className="h-7 w-7 group/cancel">
                      <XIcon className="h-4 w-4 text-destructive group-hover/cancel:text-primary-foreground" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom"><p>Cancel</p></TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          ) : (
            <div
              className="flex-grow min-w-0 font-medium text-sm cursor-pointer hover:underline underline-offset-2"
              onClick={() => setIsEditingName(true)}
              title="Click to edit configuration name"
            >
              <span className="truncate block text-foreground">{config.name}</span>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2 shrink-0">
          {/* Visibility toggle */}
          <TooltipProvider delayDuration={300}>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center">
                  <Switch
                    id={`visibility-${config.id}`}
                    checked={config.isVisible !== false}
                    onCheckedChange={async (checked) => await handleVisibilityToggle(config.id, checked)}
                    className="h-[18px] w-9 custom-switch data-[state=checked]:custom-switch-on"
                  />
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>Show in document dropdown</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Delete button */}
          <TooltipProvider delayDuration={300}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" onClick={() => handleInitiateDelete(config)} className="h-7 w-7 group/delete-btn">
                  <TrashIcon className="h-4 w-4 text-muted-foreground group-hover/delete-btn:text-destructive" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom"><p>Delete configuration</p></TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* API URL Field */}
      <div className="mb-3">
        <div className="text-xs text-muted-foreground mb-1">Endpoint URL</div>
        {isEditingApiUrl ? (
          <div className="flex items-center gap-1">
            <Input
              value={currentApiUrlValue}
              onChange={(e) => setCurrentApiUrlValue(e.target.value)}
              className="h-8 text-sm flex-grow dark:bg-input text-text-foreground"
              placeholder="https://api.example.com/v1/restyle"
              autoFocus
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  onSaveApiUrl();
                } else if (e.key === 'Escape') {
                  onCancelApiUrlEdit();
                }
              }}
            />
            <Button variant="ghost" size="icon" onClick={onSaveApiUrl} className="h-7 w-7">
              <CheckIcon className="h-4 w-4 text-primary" />
            </Button>
            <Button variant="ghost" size="icon" onClick={onCancelApiUrlEdit} className="h-7 w-7">
              <XIcon className="h-4 w-4 text-destructive" />
            </Button>
          </div>
        ) : (
          <div
            className="text-sm cursor-pointer hover:underline underline-offset-2 p-1 rounded min-h-[24px] flex items-center"
            onClick={() => setIsEditingApiUrl(true)}
            title="Click to edit endpoint URL"
          >
            <span className="text-muted-foreground">{config.apiUrl || 'Click to add endpoint URL'}</span>
          </div>
        )}
      </div>

      {/* API Key Field */}
      <div className="mb-2">
        <div className="text-xs text-muted-foreground mb-1">API Key</div>
        {isEditingApiKey ? (
          <div className="flex items-center gap-1">
            <Input
              type="text"
              value={currentApiKeyValue}
              onChange={(e) => setCurrentApiKeyValue(e.target.value)}
              className="h-8 text-sm flex-grow dark:bg-input text-text-foreground"
              placeholder="Enter API key"
              autoFocus
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  onSaveApiKey();
                } else if (e.key === 'Escape') {
                  onCancelApiKeyEdit();
                }
              }}
            />
            <Button variant="ghost" size="icon" onClick={onSaveApiKey} className="h-7 w-7">
              <CheckIcon className="h-4 w-4 text-primary" />
            </Button>
            <Button variant="ghost" size="icon" onClick={onCancelApiKeyEdit} className="h-7 w-7">
              <XIcon className="h-4 w-4 text-destructive" />
            </Button>
          </div>
        ) : (
          <div
            className="text-sm cursor-pointer hover:underline underline-offset-2 p-1 rounded min-h-[24px] flex items-center"
            onClick={() => setIsEditingApiKey(true)}
            title="Click to edit API key"
          >
            <span className="text-muted-foreground font-mono">
              {config.apiKey ? '••••••••••••••••' : 'Click to add API key'}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};
