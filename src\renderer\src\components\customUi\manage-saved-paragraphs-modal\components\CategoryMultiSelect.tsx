import React, { useState, useMemo } from 'react';
import { But<PERSON> } from '../../../ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '../../../ui/popover';
import { ScrollArea } from '../../../ui/scroll-area';
import { ChevronDown, Check } from 'lucide-react';
import { SavedParagraphCategory } from '../../../../../types/global';
import { cn } from '../../../../lib/utils';

interface CategoryMultiSelectProps {
  categories: SavedParagraphCategory[];
  selectedCategoryIds: string[];
  onSelectionChange: (selectedIds: string[]) => void;
  className?: string;
}

export const CategoryMultiSelect: React.FC<CategoryMultiSelectProps> = ({
  categories,
  selectedCategoryIds,
  onSelectionChange,
  className,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleSelectCategory = (categoryId: string) => {
    const newSelectedIds = selectedCategoryIds.includes(categoryId)
      ? selectedCategoryIds.filter(id => id !== categoryId)
      : [...selectedCategoryIds, categoryId];
    onSelectionChange(newSelectedIds);
  };

  const selectedCategoriesText = useMemo(() => {
    if (selectedCategoryIds.length === 0) {
      return 'All Categories';
    }
    if (selectedCategoryIds.length === categories.length) {
      return 'All Categories';
    }
    if (selectedCategoryIds.length === 1) {
      const selectedCategory = categories.find(c => c.id === selectedCategoryIds[0]);
      return selectedCategory ? selectedCategory.name : '1 Category';
    }
    return `${selectedCategoryIds.length} Categories`;
  }, [selectedCategoryIds, categories]);

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={isOpen}
          className={cn('w-[200px] justify-between text-foreground hover:bg-muted/50 focus:ring-ring focus:ring-offset-background focus:ring-2 focus:ring-offset-2', className)}
        >
          <span className="truncate">{selectedCategoriesText}</span>
          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0 custom-scrollbar">
        <ScrollArea className="max-h-60">
          <div className="p-1">
            {categories.map(category => (
              <div
                key={category.id}
                onClick={() => handleSelectCategory(category.id)}
                className="flex items-center justify-between p-2 rounded-md cursor-pointer hover:bg-muted/50 focus:bg-muted/50"
              >
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full border border-border/30"
                    style={{ backgroundColor: category.color || '#6B8DD6' }}
                  />
                  <span className="text-sm text-foreground">{category.name}</span>
                </div>
                {selectedCategoryIds.includes(category.id) && (
                  <Check className="h-4 w-4 text-primary" />
                )}
              </div>
            ))}
          </div>
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
};
