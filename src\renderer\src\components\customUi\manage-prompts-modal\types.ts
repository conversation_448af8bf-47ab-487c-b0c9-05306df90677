import { ProjectSettings, DocumentSettings, SystemPrompt } from '../../../../types/global';

export interface ManagePromptsModalProps {
  isOpen: boolean;
  onClose: () => void;
  projectSettings?: ProjectSettings;
  onUpdateProjectSettings?: (updatedSettings: ProjectSettings) => Promise<void>;
  activeDocumentId?: string | null;
  activeDocumentSettings?: DocumentSettings | null;
  onUpdateActiveDocumentSettings?: (updatedSettings: DocumentSettings) => Promise<void>;
}

export interface DraggablePromptItemProps {
  prompt: SystemPrompt;
  index: number;
  movePrompt: (dragIndex: number, hoverIndex: number) => void;
  activeDocumentId: string | null;
  activeDocumentSettings: DocumentSettings | null;
  handleVisibilityToggle: (promptId: string, isVisible: boolean) => void;
  handleInitiateDelete: (prompt: SystemPrompt) => void;
  handleUpdatePromptContent: (promptToUpdate: SystemPrompt, newContent: string) => Promise<void>;
  handleUpdatePromptName: (promptToUpdate: SystemPrompt, newName: string) => Promise<void>;
  onForkToLocal?: (() => void) | undefined;
  onSaveAsGlobal?: (() => void) | undefined;
  onReplaceGlobal?: (() => void) | undefined;
  onResetToGlobal?: (() => void) | undefined;
  onDetachFromGlobal?: (() => void) | undefined;
  onConvertToGlobal?: (() => void) | undefined;
  isLocal?: boolean;
  parentGlobalPrompt?: SystemPrompt | null;
  projectSettingsSystemPrompts: SystemPrompt[];
  setPromptToEdit: (prompt: SystemPrompt) => void;
  setExpandedEditorOpen: (open: boolean) => void;
}

export interface ExpandedEditorProps {
  isOpen: boolean;
  onClose: () => void;
  prompt: SystemPrompt | null;
  onUpdateContent: (prompt: SystemPrompt, newContent: string) => Promise<void>;
}

export interface TabContentProps {
  prompts: SystemPrompt[];
  newPromptName: string;
  setNewPromptName: (name: string) => void;
  onCreatePrompt: () => Promise<void>;
  movePrompt: (dragIndex: number, hoverIndex: number) => void;
  activeDocumentId: string | null;
  activeDocumentSettings: DocumentSettings | null;
  handleVisibilityToggle: (promptId: string, isVisible: boolean) => void;
  handleInitiateDelete: (prompt: SystemPrompt) => void;
  handleUpdatePromptContent: (promptToUpdate: SystemPrompt, newContent: string) => Promise<void>;
  handleUpdatePromptName: (promptToUpdate: SystemPrompt, newName: string) => Promise<void>;
  projectSettingsSystemPrompts: SystemPrompt[];
  setPromptToEdit: (prompt: SystemPrompt) => void;
  setExpandedEditorOpen: (open: boolean) => void;
  promptActions?: PromptActionHandlers;
}

export interface PromptActionHandlers {
  onForkToLocal?: (prompt: SystemPrompt) => Promise<void>;
  onSaveAsGlobal?: (prompt: SystemPrompt) => Promise<void>;
  onReplaceGlobal?: (prompt: SystemPrompt) => Promise<void>;
  onResetToGlobal?: (prompt: SystemPrompt) => Promise<void>;
  onDetachFromGlobal?: (prompt: SystemPrompt) => Promise<void>;
  onConvertToGlobal?: (prompt: SystemPrompt) => Promise<void>;
}

export interface DeleteConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  promptName: string;
}

export const DND_ITEM_TYPE_PROMPT = 'systemPrompt';
