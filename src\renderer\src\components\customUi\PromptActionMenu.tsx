import React from 'react';
import {
  MoreH<PERSON>zontal,
  Copy,
  Upload,
  RefreshCw,
  RotateCcw,
  FilePlus,
  ArrowUpRight,
  Unlink
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider
} from '../ui/tooltip';
import { Button } from '../ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '../ui/dropdown-menu';
import { SystemPrompt } from '../../../types/global';
import { useSettingsSidebar } from './sidebar-right/SettingsSidebarContext';

interface PromptActionMenuProps {
  prompt: SystemPrompt;
  isLocal: boolean;
  onForkToLocal: () => void;
  onSaveAsGlobal: () => void;
  onReplaceGlobal: () => void;
  onResetToGlobal: () => void;
  onDetachFromGlobal?: () => void;
  onConvertToGlobal?: () => void;
  hasParentGlobal: boolean;
}

export function PromptActionMenu({
  prompt,
  isLocal,
  onForkToLocal,
  onSaveAsGlobal,
  onReplaceGlobal,
  onResetToGlobal,
  onDetachFromGlobal = () => {},
  onConvertToGlobal = () => {},
  hasParentGlobal
}: PromptActionMenuProps) {
  const { setIsUiExtensionOpen } = useSettingsSidebar();

  // Prevent event propagation
  const handleStopPropagation = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  // Handle dropdown state changes without interfering with modal state
  const handleDropdownOpenChange = (open: boolean) => {
    // Only set UI extension to true when opening, don't set to false when closing
    // This prevents the dropdown from collapsing the sidebar while the modal is open
    if (open) {
      setIsUiExtensionOpen(true);
    }
  };

  return (
    <DropdownMenu onOpenChange={handleDropdownOpenChange}>
      {/* Fixed: Using Button directly as DropdownMenuTrigger */}
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="h-7 w-7"
          onClick={handleStopPropagation}
          aria-label="Prompt actions"
        >
          <MoreHorizontal className="h-4 w-4 text-muted-foreground" />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align="end" className="w-64" onCloseAutoFocus={(e) => e.preventDefault()}>
        {/* Options for GLOBAL prompts */}
        {!isLocal && (
          <>
            <DropdownMenuItem
              className="flex items-center gap-2 cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                onForkToLocal();
              }}
            >
              <Copy className="h-4 w-4" />
              <span>Duplicate to Document Prompts</span>
            </DropdownMenuItem>
          </>
        )}

        {/* Options for DOCUMENT prompts */}
        {isLocal && (
          <>
            {/* Document prompts FORKED from global */}
            {hasParentGlobal && (
              <>
                <DropdownMenuItem
                  className="flex items-center gap-2 cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    onResetToGlobal();
                  }}
                >
                  <RotateCcw className="h-4 w-4" />
                  <span>Reset to Global</span>
                </DropdownMenuItem>

                <DropdownMenuItem
                  className="flex items-center gap-2 cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    onSaveAsGlobal();
                  }}
                >
                  <FilePlus className="h-4 w-4" />
                  <span>Save as New Global</span>
                </DropdownMenuItem>

                <DropdownMenuItem
                  className="flex items-center gap-2 cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    onReplaceGlobal();
                  }}
                >
                  <RefreshCw className="h-4 w-4" />
                  <span>Update Source Global</span>
                </DropdownMenuItem>

                <DropdownMenuItem
                  className="flex items-center gap-2 cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDetachFromGlobal();
                  }}
                >
                  <Unlink className="h-4 w-4" />
                  <span>Detach from Global</span>
                </DropdownMenuItem>

                <DropdownMenuSeparator />
              </>
            )}

            {/* All document-specific prompts */}
            <DropdownMenuItem
              className="flex items-center gap-2 cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                onConvertToGlobal();
              }}
            >
              <ArrowUpRight className="h-4 w-4" />
              <span>Convert to Global</span>
            </DropdownMenuItem>

            <DropdownMenuItem
              className="flex items-center gap-2 cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                onSaveAsGlobal();
              }}
            >
              <Upload className="h-4 w-4" />
              <span>Save as Global</span>
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
