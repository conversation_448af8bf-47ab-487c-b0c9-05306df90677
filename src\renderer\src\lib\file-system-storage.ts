'use client';

import { nanoid } from 'nanoid';
import { FrontendStoredFile, DocumentSettings, ProjectSettings } from './../../types/global';

// The entire 'declare global { interface Window { fileStorage: ... } }' block
// that was previously here MUST be removed.
// It has been replaced by the definitions in src/types/global.d.ts.

export interface FileSystemStorageOptions {
  autoSaveInterval?: number; // milliseconds
}

// StoredFileInfo interface should have been removed/commented out in previous steps,
// and FrontendStoredFile is imported above.

const STORAGE_DIR_KEY = 'plate-editor-storage-dir';

class FileSystemStorage {
  private storageHandle: FileSystemDirectoryHandle | null = null;
  private autoSaveInterval: number;
  private autoSaveTimer: NodeJS.Timeout | null = null;
  private pendingSave: (() => Promise<void>) | null = null;
  private isSaving = false;
  public isDirectorySelected: boolean = false;
  private currentStoragePath: string | null = null;
  private listeners: Array<() => void> = [];
  private readyPromise: Promise<void>;
  private resolveReady!: () => void; // Definite assignment assertion

  constructor(options: FileSystemStorageOptions = {}) {
    this.autoSaveInterval = options.autoSaveInterval || 30000;
    this.readyPromise = new Promise((resolve) => {
      this.resolveReady = resolve;
    });
    this.loadPersistedStoragePath().then(() => {
      this.resolveReady();
    }).catch(error => {
      console.error("FileSystemStorage: Error during initial path loading:", error);
      this.resolveReady(); // Still resolve to allow app to proceed, albeit possibly without a selected directory
    });
  }

  private async loadPersistedStoragePath(): Promise<void> {
    try {
      if (typeof window !== 'undefined' && window.fileStorage) {
        const path = await window.fileStorage.getStoragePath();
        if (path) {
          this.currentStoragePath = path;
          this.isDirectorySelected = true;
          this.notifyListeners();
        } else {
          this.isDirectorySelected = false;
        }
      } else {
        // If window.fileStorage is not available yet, set up a retry mechanism
        let retries = 0;
        const maxRetries = 50; // 5 seconds with 100ms intervals

        while (retries < maxRetries && (!window.fileStorage)) {
          await new Promise(resolve => setTimeout(resolve, 100));
          retries++;
        }

        if (window.fileStorage) {
          const path = await window.fileStorage.getStoragePath();
          if (path) {
            this.currentStoragePath = path;
            this.isDirectorySelected = true;
            this.notifyListeners();
          } else {
            this.isDirectorySelected = false;
          }
        } else {
          console.warn('FileSystemStorage: window.fileStorage not available after retries');
          this.isDirectorySelected = false;
        }
      }
    } catch (error) {
      console.error("Error loading persisted storage path:", error);
      this.isDirectorySelected = false;
      this.notifyListeners();
    }
  }

  subscribe(listener: () => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener());
  }

  async selectDirectory(): Promise<string | null> {
    try {
      const newPath = await window.fileStorage.chooseStorageDirectory();
      if (newPath) {
        this.currentStoragePath = newPath;
        this.isDirectorySelected = true;
        this.notifyListeners();
        return newPath;
      }
      this.isDirectorySelected = false;
      this.notifyListeners();
      return null;
    } catch (error) {
      console.error('Error selecting directory:', error);
      this.isDirectorySelected = false;
      this.notifyListeners();
      throw error;
    }
  }

  async clearStoragePath(): Promise<void> {
    try {
      await window.fileStorage.clearStoragePath();
      this.currentStoragePath = null;
      this.isDirectorySelected = false;
      this.notifyListeners();
    } catch (error) {
      console.error('Error clearing storage path:', error);
      throw error;
    }
  }

  async listFiles(): Promise<FrontendStoredFile[]> {
    await this.readyPromise;
    if (!this.isDirectorySelected) {
      return [];
    }
    try {
      return await window.fileStorage.getFiles();
    } catch (error) {
      console.error('Error listing files:', error);
      throw error;
    }
  }

  async loadFile(id: string): Promise<FrontendStoredFile | null> {
    await this.readyPromise;
    if (!this.isDirectorySelected) return null;
    try {
      return await window.fileStorage.getFileById(id);
    } catch (error) {
      console.error(`Error loading file ${id}:`, error);
      throw error;
    }
  }

  // // Old saveFile - to be deprecated or adapted if non-document types are still directly saved this way.
  // // For documents, use saveDocumentContent and saveDocumentMetadata.
  // async saveFile(name: string, dataToSave: { mainContent?: string; documentSettings?: DocumentSettings }, type: 'document' | 'html' | 'markdown' | 'json'): Promise<FrontendStoredFile> {
  //   if (!this.isDirectorySelected) throw new Error('Storage directory not selected.');
  //   try {
  //     // If type === 'document', this is problematic with the new structure.
  //     // Consider throwing an error or delegating to new methods based on dataToSave structure.
  //     console.warn('Legacy saveFile called. Consider using saveDocumentContent/saveDocumentMetadata for document types.');
  //     return await window.fileStorage.saveFile(name, dataToSave, type);
  //   } catch (error) {
  //     console.error('Error saving file:', error);
  //     throw error;
  //   }
  // }

  async saveDocumentContent(docId: string, content: string): Promise<void> {
    await this.readyPromise;
    if (!this.isDirectorySelected) throw new Error('Storage directory not selected.');
    try {
      return await window.fileStorage.saveDocumentContent(docId, content);
    } catch (error) {
      console.error(`Error saving document content for ${docId}:`, error);
      throw error;
    }
  }

  async saveDocumentMetadata(docId: string, metadata: DocumentSettings): Promise<void> {
    await this.readyPromise;
    if (!this.isDirectorySelected) throw new Error('Storage directory not selected.');
    try {
      return await window.fileStorage.saveDocumentMetadata(docId, metadata);
    } catch (error) {
      console.error(`Error saving document metadata for ${docId}:`, error);
      throw error;
    }
  }

  async deleteFile(id: string): Promise<{ success: boolean; deletedIds: string[] }> {
    await this.readyPromise;
    if (!this.isDirectorySelected) throw new Error('Storage directory not selected.');
    try {
      return await window.fileStorage.deleteFile(id);
    } catch (error) {
      console.error('Error deleting file:', error);
      throw error;
    }
  }

  // Renamed from createFile and adapted for new backend
  async createDocumentInWorkspace(documentName: string, parentId?: string | null): Promise<FrontendStoredFile> {
    await this.readyPromise;
    if (!this.isDirectorySelected) throw new Error('Storage directory not selected.');
    try {
      return await window.fileStorage.createDocument(documentName, parentId);
    } catch (error) {
      console.error('Error creating document:', error);
      throw error;
    }
  }

  async renameFile(fileId: string, newName: string): Promise<{renamedItem: FrontendStoredFile, updatedChildren?: FrontendStoredFile[]}> {
    await this.readyPromise;
    if (!this.isDirectorySelected) throw new Error('Storage directory not selected.');
    try {
      return await window.fileStorage.renameFile(fileId, newName);
    } catch (error) {
      console.error('Error renaming file:', error);
      throw error;
    }
  }

  async duplicateFile(fileIdToDuplicate: string, suggestedNewName: string): Promise<FrontendStoredFile> {
    await this.readyPromise;
    if (!this.isDirectorySelected) throw new Error('Storage directory not selected.');
    try {
      return await window.fileStorage.duplicateFile(fileIdToDuplicate, suggestedNewName);
    } catch (error) {
      console.error('Error duplicating file:', error);
      throw error;
    }
  }

  async createFolder(folderName: string, parentId?: string | null): Promise<FrontendStoredFile> {
     if (!this.isDirectorySelected) throw new Error('Storage directory not selected.');
     try {
       return await window.fileStorage.createFolder(folderName, parentId);
     } catch (error) {
       console.error('Error creating folder:', error);
       throw error;
     }
  }

  async exportToDisk(fileToExport: FrontendStoredFile): Promise<boolean> {
    if (!this.isDirectorySelected) throw new Error('Storage directory not selected.');
    try {
      return await window.fileStorage.exportFile(fileToExport);
    } catch (error) {
      console.error('Error exporting file:', error);
      throw error;
    }
  }

  async moveItem(itemId: string, newParentId: string | null): Promise<{movedItem: FrontendStoredFile, updatedChildren?: FrontendStoredFile[]}> {
    if (!this.isDirectorySelected) throw new Error('Storage directory not selected.');
    try {
      const result = await window.fileStorage.moveItem(itemId, newParentId);
      // if (!result) throw new Error('Move operation returned null or undefined'); // Already handled by main process error or success
      this.notifyListeners(); // Notify that the file list might have changed due to re-parenting
      return result;
    } catch (error) {
      console.error('Error moving item in FileSystemStorage:', error);
      throw error;
    }
  }

  getStoragePath(): string | null {
    return this.currentStoragePath;
  }

  startAutoSave(saveFunction: () => Promise<void>): void {
    console.warn("FileSystemStorage.startAutoSave called - this should ideally be handled by AutoSaveManager itself. Starting a basic interval timer.");
  }

  stopAutoSave(): void {
    console.warn("FileSystemStorage.stopAutoSave called - this should ideally be handled by AutoSaveManager itself. Clearing internal timer if any.");
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
      this.autoSaveTimer = null;
    }
  }

  async loadProjectSettings(): Promise<ProjectSettings> {
    if (!this.isDirectorySelected && !this.getStoragePath()) {
        // If no directory is selected yet, we might still want to attempt to load if a path was persisted from a previous session
        // However, getStoragePath() is called in constructor. If path is available, isDirectorySelected should be true.
        // This check is more of a safeguard or for contexts where loadPersistedStoragePath might not have completed yet.
        // Let's rely on isDirectorySelected for now, or ensure loadPersistedStoragePath completes.
        // For now, if no dir selected, throw error or return default empty. The main process returns defaults if not found.
        console.warn('Attempting to load project settings without a selected storage directory. This might rely on persisted path only.');
        // Fall through to let the main process handle path absence, it has better context.
    }
    try {
      return await window.fileStorage.loadProjectSettings();
    } catch (error) {
      console.error('Error loading project settings:', error);
      // Consider returning a default empty ProjectSettings object or re-throwing
      // As the main process already provides defaults if file is missing, re-throwing is probably better.
      throw error;
    }
  }

  async saveProjectSettings(settings: ProjectSettings): Promise<void> {
    if (!this.isDirectorySelected) throw new Error('Storage directory not selected. Cannot save project settings.');
    try {
      return await window.fileStorage.saveProjectSettings(settings);
    } catch (error) {
      console.error('Error saving project settings:', error);
      throw error;
    }
  }
}

const fileSystemStorage = new FileSystemStorage();
export default fileSystemStorage;
