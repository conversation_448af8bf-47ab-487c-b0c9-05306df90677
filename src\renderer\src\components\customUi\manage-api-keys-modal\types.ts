import { ProjectSettings, RestyleConfig } from '../../../../types/global';

export interface ManageApiKeysModalProps {
  isOpen: boolean;
  onClose: () => void;
  projectSettings?: ProjectSettings;
  onUpdateProjectSettings?: (updatedSettings: ProjectSettings) => Promise<void>;
}

export interface ApiKeyItemProps {
  config: RestyleConfig;
  index: number;
  moveConfig: (dragIndex: number, hoverIndex: number) => Promise<void>;
  handleVisibilityToggle: (configId: string, isVisible: boolean) => Promise<void>;
  handleInitiateDelete: (config: RestyleConfig) => void;
  handleUpdateConfigField: (configToUpdate: RestyleConfig, field: keyof RestyleConfig, newValue: string) => Promise<void>;
}

export interface DeleteConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  configName: string;
}

export const DND_ITEM_TYPE_API_KEY = 'restyleConfig';
