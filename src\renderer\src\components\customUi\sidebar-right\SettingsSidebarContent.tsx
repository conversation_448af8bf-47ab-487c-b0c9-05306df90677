import React from 'react';
import { InfoIcon } from 'lucide-react';
import { ScrollArea } from '../../ui/scroll-area';
import { SettingsSidebarContentProps, SavedParagraphsSectionProps } from './SettingsSidebarTypes';
import { SettingsSidebarDocumentSettings } from './SettingsSidebarDocumentSettings';
import { SettingsSidebarPrompts } from './SettingsSidebarPrompts';
import { SettingsSidebarSavedParagraphs } from './SettingsSidebarSavedParagraphs';

export function SettingsSidebarContent({
  activeFile,
  projectSettings,
  documentSettings,
  onUpdateProjectSettings,
  onSaveActiveDocumentSettings,
  isUiExtensionOpen,
  setIsUiExtensionOpen
}: SettingsSidebarContentProps) {
  return (
    <ScrollArea className="flex-1">
      <div className="p-4 space-y-8 pb-[60px]">
        {/* Message when no document is active */}
        {!activeFile && (
          <div className="text-center py-10 px-4">
            <InfoIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-foreground">No Document Selected</h3>
            <p className="mt-1 text-sm text-foreground">Open or create a document to manage its settings here.</p>
          </div>
        )}

        {/* Settings content when a document is active */}
        {activeFile && projectSettings && documentSettings && (
          <>
            <SettingsSidebarDocumentSettings
              activeFile={activeFile}
              projectSettings={projectSettings}
              documentSettings={documentSettings}
              onSaveActiveDocumentSettings={onSaveActiveDocumentSettings}
              onUpdateProjectSettings={onUpdateProjectSettings}
              setIsUiExtensionOpen={setIsUiExtensionOpen}
            />

            <SettingsSidebarSavedParagraphs />

            <SettingsSidebarPrompts
              activeFile={activeFile}
              projectSettings={projectSettings}
              documentSettings={documentSettings}
              onUpdateProjectSettings={onUpdateProjectSettings}
              onSaveActiveDocumentSettings={onSaveActiveDocumentSettings}
            />
          </>
        )}
      </div>
    </ScrollArea>
  );
}
