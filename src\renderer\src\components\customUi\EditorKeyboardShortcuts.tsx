import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Command, ChevronRight } from 'lucide-react';

interface ShortcutDefinition {
  label: string;
  shortcut: string;
  description?: string;
  important?: boolean;
}

// Define all editor shortcuts with priority levels
const EDITOR_SHORTCUTS: ShortcutDefinition[] = [
  { label: 'Save', shortcut: '⌘ + S' },
  { label: 'Copy', shortcut: '⌘ + C', important: true },
  { label: 'Copy Markdown', shortcut: 'Shift + C', important: true },
  { label: 'Copy Markdown + Prompt', shortcut: '⌘ + Shift + C', important: true },
  { label: 'Bold', shortcut: '⌘ + B' },
  { label: 'Italic', shortcut: '⌘ + I' },
  { label: 'Undo', shortcut: '⌘ + Z' },
  { label: 'Underline', shortcut: '⌘ + U' },
  { label: 'Heading 1', shortcut: '⌘ + Alt + 1', description: 'Also works with 2 and 3' },
  { label: 'Bullet List', shortcut: '⌘ + Shift + 8', description: 'Or start line with *' },
  { label: 'Numbered List', shortcut: '⌘ + Shift + 7', description: 'Or start line with 1.' },
  { label: 'Insert Link', shortcut: '⌘ + K' },
  { label: 'Code Block', shortcut: '⌘ + Shift + K', description: 'Or wrap with ```' },
  { label: 'Redo', shortcut: '⌘ + Shift + Z' }
];

type ShortcutState = 'closed' | 'compact' | 'expanded';

const EditorKeyboardShortcuts: React.FC = () => {
  const [state, setState] = useState<ShortcutState>('compact');

  const importantShortcuts = EDITOR_SHORTCUTS.filter(s => s.important);

  const handleToggle = () => {
    setState(current => {
      switch (current) {
        case 'closed': return 'compact';
        case 'compact': return 'expanded';
        case 'expanded': return 'closed';
        default: return 'closed';
      }
    });
  };

  return (
    <div className="fixed bottom-4 right-4 z-50 flex items-end">
      <AnimatePresence mode="wait">
        {state !== 'closed' && (
          <motion.div
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 100 }}
            transition={{ duration: 0.2 }}
            className="mr-2 bg-background border border-border rounded-lg shadow-lg overflow-hidden"
          >
            {state === 'compact' ? (
              <div className="flex items-center px-4 py-2 gap-6">
                {importantShortcuts.map((shortcut, index) => (
                  <div key={shortcut.label} className="flex items-center gap-2">
                    <span className="text-xs text-muted-foreground">{shortcut.label}:</span>
                    <span className="text-xs font-mono bg-muted px-1.5 py-0.5 rounded text-muted-foreground">
                      {shortcut.shortcut}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-4 max-w-4xl">
                <h3 className="font-medium text-sm mb-3 text-foreground">Keyboard Shortcuts</h3>
                <div className="grid grid-cols-3 gap-x-6 gap-y-2 max-h-60 overflow-y-auto custom-scrollbar pr-2">
                  {EDITOR_SHORTCUTS.map((shortcut) => (
                    <div key={shortcut.label} className="flex flex-col gap-1">
                      <div className="flex items-center justify-between gap-2">
                        <span className="text-sm text-foreground font-medium">{shortcut.label}</span>
                        <span className="text-xs font-mono bg-muted px-1.5 py-0.5 rounded text-muted-foreground whitespace-nowrap">
                          {shortcut.shortcut}
                        </span>
                      </div>
                      {shortcut.description && (
                        <p className="text-xs text-muted-foreground">{shortcut.description}</p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Toggle Button - Always visible in bottom right */}
      <motion.button
        onClick={handleToggle}
        className="bg-accent text-accent-foreground rounded-full w-10 h-10 flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        aria-label={
          state === 'closed'
            ? 'Show keyboard shortcuts'
            : state === 'compact'
              ? 'Expand shortcuts'
              : 'Close shortcuts'
        }
      >
        <AnimatePresence mode="wait">
          {state === 'closed' ? (
            <motion.div
              key="command"
              initial={{ opacity: 0, rotate: -90 }}
              animate={{ opacity: 1, rotate: 0 }}
              exit={{ opacity: 0, rotate: 90 }}
              transition={{ duration: 0.2 }}
            >
              <Command className="h-5 w-5" />
            </motion.div>
          ) : state === 'compact' ? (
            <motion.div
              key="expand"
              initial={{ opacity: 0, rotate: -90 }}
              animate={{ opacity: 1, rotate: 0 }}
              exit={{ opacity: 0, rotate: 90 }}
              transition={{ duration: 0.2 }}
            >
              <ChevronRight className="h-5 w-5" />
            </motion.div>
          ) : (
            <motion.div
              key="close"
              initial={{ opacity: 0, rotate: -90 }}
              animate={{ opacity: 1, rotate: 0 }}
              exit={{ opacity: 0, rotate: 90 }}
              transition={{ duration: 0.2 }}
            >
              <X className="h-5 w-5" />
            </motion.div>
          )}
        </AnimatePresence>
      </motion.button>
    </div>
  );
};

export default React.memo(EditorKeyboardShortcuts);
