import React from 'react';
import { Moon, Sun, Laptop, Minus, Square, X, CloudMoon } from 'lucide-react';
import AppLogoSvg from './AppLogoSvg';
import UpdateStatus from './UpdateStatus';
import { useTheme } from '../../hooks/useTheme';
import { ThemeMode } from '../../contexts/ThemeContext';

const CustomTitleBar: React.FC = () => {
  const { theme, isDarkMode, toggleTheme, setTheme } = useTheme();

  // Use CSS variables for logo colors instead of hardcoded values
  const currentLogoColors = {
    fill: "transparent",
    stroke: "var(--foreground)",  // This will automatically use the foreground color from current theme
    eyes: "var(--foreground)"
  };

  const handleWindowAction = (action: 'minimize' | 'maximize' | 'close') => {
    if (window.electron && window.electron.ipcRenderer) {
      window.electron.ipcRenderer.send('window-action', action);
    } else {
      console.warn('window.electron.ipcRenderer is not available. Running in browser?');
    }
  };

  const handleThemeToggle = () => {
    // Enhanced toggle that cycles through all three themes
    const themeOrder: ThemeMode[] = ['light', 'dark', 'custom'];
    const currentIndex = themeOrder.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themeOrder.length;
    setTheme(themeOrder[nextIndex]);
  };

  // Get the appropriate theme icon based on current theme
  const ThemeIcon = () => {
    if (theme === 'light') return <Sun size={iconSize} />;
    if (theme === 'dark') return <Moon size={iconSize} />;
    return <CloudMoon size={iconSize} />; // For 'custom' theme (Ayu Mirage)
  };

  const buttonBaseClass = "p-2 rounded-md focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background hover:bg-foreground/10 active:bg-foreground/15";
  const iconSize = 16;

  // Determine theme name for display
  const getThemeName = () => {
    if (theme === 'light') return 'Light (Ayu Light)';
    if (theme === 'dark') return 'Dark (One Dark Pro)';
    return 'Mirage (Ayu Mirage)';
  };

  return (
    <div className="h-[30px] bg-background text-foreground flex items-center justify-between select-none border-b border-border fixed top-0 left-0 right-0 z-50" style={{ WebkitAppRegion: 'drag' } as React.CSSProperties}>
      <div className="flex items-center pl-2 space-x-2">
        <AppLogoSvg
          width={20}
          height={20}
          className="w-5 h-5"
          fillColor={currentLogoColors.fill}
          strokeColor={currentLogoColors.stroke}
          eyeColor={currentLogoColors.eyes}
          strokeWidthValue={30.8}
        />
        <span className="text-sm font-light">TruffleNote</span>
      </div>
      <div className="flex items-center" style={{ WebkitAppRegion: 'no-drag' } as React.CSSProperties}>
        <UpdateStatus className="mr-1" />
        <button
          onClick={handleThemeToggle}
          className={`${buttonBaseClass} mr-1`}
          aria-label="Change theme"
          title={`Current theme: ${getThemeName()}. Click to cycle themes.`}
        >
          <ThemeIcon />
        </button>
        <button
          onClick={() => handleWindowAction('minimize')}
          className={`${buttonBaseClass}`}
          aria-label="Minimize"
        >
          <Minus size={iconSize} />
        </button>
        <button
          onClick={() => handleWindowAction('maximize')}
          className={`${buttonBaseClass}`}
          aria-label="Maximize"
        >
          <Square size={iconSize} />
        </button>
        <button
          onClick={() => handleWindowAction('close')}
          className={`${buttonBaseClass} hover:bg-destructive hover:text-destructive-foreground active:bg-destructive/90 mr-1`}
          aria-label="Close"
        >
          <X size={iconSize} />
        </button>
      </div>
    </div>
  );
};

export default CustomTitleBar;
