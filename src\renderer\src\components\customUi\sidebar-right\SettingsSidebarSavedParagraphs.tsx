import React, { useCallback } from 'react';
import { But<PERSON> } from '../../ui/button';
import { Settings, BookOpen } from 'lucide-react';
import { SavedParagraphsSectionProps } from './SettingsSidebarTypes'; // Import the new props type
import { useSavedParagraphsStore } from '../../../stores/savedParagraphsStore';

export function SettingsSidebarSavedParagraphs({}: SavedParagraphsSectionProps) {
  const { openModalWithView, isModalOpen } = useSavedParagraphsStore();

  const handleOpenModal = useCallback((event: React.MouseEvent) => {
    // Prevent event bubbling and default behavior
    event.preventDefault();
    event.stopPropagation();

    // Prevent multiple rapid clicks
    if (isModalOpen) {
      return;
    }

    // Use atomic operation to set view and open modal in single state update
    openModalWithView('list');
    // Note: UI extension state is now automatically managed by SettingsSidebarContext
  }, [openModalWithView, isModalOpen]);

  return (
    <>
      <div className="space-y-4">
        <div className="space-y-2">
          <h4 className="font-medium text-sm text-foreground">Saved Paragraphs</h4>
        </div>

        <div className="space-y-2">
          <Button
            variant="outline"
            onClick={handleOpenModal} // Use new handler
            className="w-full max-w-[240px] justify-center border border-foreground/50 hover:border-foreground/30"
          >
            <Settings className="h-4 w-4 mr-2" />
            Manage Paragraphs
          </Button>

          <div className="text-xs text-muted-foreground space-y-1">
            <div className="flex items-center gap-2">
              <BookOpen className="h-3 w-3" />
              <span>Press <kbd className="px-1 py-0.5 bg-muted rounded text-xs">/</kbd> to insert saved paragraphs</span>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
