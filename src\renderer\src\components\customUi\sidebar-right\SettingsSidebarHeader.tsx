import React from 'react';
import { PinIcon, PinOffIcon, ChevronRightIcon } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from '../../ui/tooltip';
import { Button } from '../../ui/button';
import { SettingsSidebarHeaderProps } from './SettingsSidebarTypes';

export function SettingsSidebarHeader({
  isPinned,
  togglePinned,
  toggleExpanded
}: SettingsSidebarHeaderProps) {
  return (
    <div className="flex items-center justify-between p-1.5 border-b border-border sticky top-0 bg-background z-10 h-[50px] flex-shrink-0">
      <div className="flex items-center gap-0.5">
        <span className="font-semibold text-sm ml-2 text-foreground">Settings</span>
      </div>
      <div className="flex items-center gap-0.5 mr-1">
        <TooltipProvider delayDuration={300}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="icon" className="h-7 w-7" onClick={togglePinned}>
                {isPinned ? (
                  <PinOffIcon className="h-4 w-4 text-muted-foreground" />
                ) : (
                  <PinIcon className="h-4 w-4 text-muted-foreground" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p>{isPinned ? 'Unpin settings sidebar' : 'Pin settings sidebar'}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <button
          className="h-8 w-8 flex items-center justify-center rounded-md hover:bg-slate-100 dark:hover:bg-slate-800"
          onClick={toggleExpanded}
          title="Collapse Settings"
        >
          <ChevronRightIcon className="h-4 w-4 text-primary" />
        </button>
      </div>
    </div>
  );
}
