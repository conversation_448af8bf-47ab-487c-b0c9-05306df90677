import React from 'react';
import { SettingsIcon } from 'lucide-react';
import { Button } from '../../ui/button';
import { useSettingsSidebar } from './SettingsSidebarContext';

export function SettingsSidebarButton() {
  const { expanded, toggleExpanded, isPinned } = useSettingsSidebar();

  if (isPinned && expanded) {
    return null;
  }

  return (
    <Button
      variant="outline"
      size="icon"
      onClick={toggleExpanded}
      className="fixed bottom-4 right-4 z-50 h-10 w-10 rounded-full shadow-lg dark:bg-slate-700 dark:border-slate-600 dark:text-slate-300 dark:hover:bg-slate-600"
      aria-label="Toggle Settings Sidebar"
    >
      <SettingsIcon className="h-5 w-5" />
    </Button>
  );
}
