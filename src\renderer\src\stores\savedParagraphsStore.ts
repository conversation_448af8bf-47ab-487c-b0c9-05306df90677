import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { enableMapSet } from 'immer';
import { SavedParagraphMetadata, SavedParagraphCategory } from '../../types/global';

// Enable Map and Set support in immer
enableMapSet();

interface SavedParagraphsState {
  // Data state
  paragraphs: SavedParagraphMetadata[];
  categories: SavedParagraphCategory[];

  // UI state
  isModalOpen: boolean;
  selectedParagraphId: string | null;
  selectedCategoryIds: string[];
  searchQuery: string;
  isLoading: boolean;
  error: string | null;

  // Modal view state
  currentView: 'list' | 'create' | 'edit' | 'category-management' | 'update' | 'update-edit' | 'insert';

  // Initial content for create mode
  initialCreateContent: any[] | null;

  // Search cache for full content
  searchContentCache: Map<string, string>;

  // Actions
  initializeData: () => Promise<void>;
  loadParagraphs: () => Promise<void>;
  loadCategories: () => Promise<void>;

  // Paragraph actions
  createParagraph: (title: string, categoryId: string, content: any, description?: string, tags?: string[]) => Promise<void>;
  updateParagraph: (id: string, updates: Partial<SavedParagraphMetadata>, content?: any) => Promise<void>;
  deleteParagraph: (id: string) => Promise<void>;

  // Category actions
  createCategory: (name: string, color?: string) => Promise<void>;
  updateCategory: (id: string, updates: Partial<SavedParagraphCategory>) => Promise<void>;
  deleteCategory: (id: string) => Promise<void>;

  // UI actions
  openModal: () => void;
  openModalWithView: (view: 'list' | 'create' | 'edit' | 'category-management' | 'update' | 'update-edit' | 'insert') => void;
  closeModal: () => void;
  setSelectedParagraph: (id: string | null) => void;
  setSelectedCategory: (ids: string[]) => void;
  setSearchQuery: (query: string) => void;
  setCurrentView: (view: 'list' | 'create' | 'edit' | 'category-management' | 'update' | 'update-edit' | 'insert') => void;
  setInitialCreateContent: (content: any[] | null) => void;
  clearError: () => void;

  // Helper methods
  getParagraphsByCategory: (categoryId: string) => SavedParagraphMetadata[];
  getFilteredParagraphs: () => Promise<SavedParagraphMetadata[]>;
  getCategoryById: (id: string) => SavedParagraphCategory | undefined;
  getParagraphById: (id: string) => SavedParagraphMetadata | undefined;
  generateContentPreview: (content: any) => string;
}

// Helper function to extract full text content from BlockNote content
const extractFullTextContent = (content: any): string => {
  if (!content) return '';

  // Handle BlockNote content structure
  if (Array.isArray(content)) {
    const textContent = content
      .map(block => {
        if (block.content && Array.isArray(block.content)) {
          return block.content
            .map((item: any) => item.text || '')
            .join(' ');
        }
        return '';
      })
      .join(' ')
      .trim();

    return textContent;
  }

  // Handle plain text or other formats
  return typeof content === 'string' ? content : JSON.stringify(content);
};

// Helper function to generate content preview
const generateContentPreview = (content: any): string => {
  const fullText = extractFullTextContent(content);
  return fullText.slice(0, 100) + (fullText.length > 100 ? '...' : '');
};

// Helper function to count words and characters
const getContentStats = (content: any): { wordCount: number; characterCount: number } => {
  const preview = generateContentPreview(content);
  const wordCount = preview.trim() ? preview.trim().split(/\s+/).length : 0;
  const characterCount = preview.length;

  return { wordCount, characterCount };
};

export const useSavedParagraphsStore = create<SavedParagraphsState>()(
  devtools(
    subscribeWithSelector(
      immer((set, get) => ({
        // Initial state
        paragraphs: [],
        categories: [],
        isModalOpen: false,
        selectedParagraphId: null,
        selectedCategoryIds: [],
        searchQuery: '',
        isLoading: false,
        error: null,
        currentView: 'list',
        initialCreateContent: null,
        searchContentCache: new Map(),

        // Initialize both paragraphs and categories data
        initializeData: async () => {
          console.log('🏪 [Store] Starting store initialization...');
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          try {
            // Check if fileStorage API is available
            if (!window.fileStorage) {
              throw new Error('File storage API not available');
            }

            // Add timeout to prevent hanging
            const timeoutPromise = new Promise((_, reject) => {
              setTimeout(() => reject(new Error('Operation timed out after 10 seconds')), 10000);
            });

            console.log('📚 [Store] Loading categories...');
            console.log('📝 [Store] Loading paragraphs...');
            const dataPromise = Promise.all([
              window.fileStorage.getSavedParagraphs(),
              window.fileStorage.getSavedParagraphCategories()
            ]);

            const [paragraphs, categories] = await Promise.race([dataPromise, timeoutPromise]) as [any[], any[]];

            set((state) => {
              state.paragraphs = paragraphs;
              state.categories = categories;
              state.isLoading = false;
              console.log('🏁 [Store] Setting loading to false');
            });
            console.log('✅ [Store] Store initialization completed');
          } catch (error) {
            console.error('[SavedParagraphs] Error in initializeData:', error);

            set((state) => {
              state.error = error instanceof Error ? error.message : 'Failed to load data';
              state.isLoading = false;
              console.log('🏁 [Store] Setting loading to false');
            });
          }
        },

        // Load data
        loadParagraphs: async () => {
          try {
            const paragraphs = await window.fileStorage.getSavedParagraphs();
            set((state) => {
              state.paragraphs = paragraphs;
            });
          } catch (error) {
            set((state) => {
              state.error = error instanceof Error ? error.message : 'Failed to load paragraphs';
            });
          }
        },

        loadCategories: async () => {
          try {
            const categories = await window.fileStorage.getSavedParagraphCategories();
            set((state) => {
              state.categories = categories;
            });
          } catch (error) {
            set((state) => {
              state.error = error instanceof Error ? error.message : 'Failed to load categories';
            });
          }
        },

        // Paragraph actions
        createParagraph: async (title: string, categoryId: string, content: any, description?: string, tags?: string[]) => {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          try {
            const { wordCount, characterCount } = getContentStats(content);
            const contentPreview = generateContentPreview(content);

            const newParagraph = await window.fileStorage.saveParagraph({
              title,
              categoryId,
              description,
              tags,
              contentPreview,
              wordCount,
              characterCount
            }, content);

            set((state) => {
              state.paragraphs.push(newParagraph);
              state.isLoading = false;
              state.currentView = 'list';
            });
          } catch (error) {
            set((state) => {
              state.error = error instanceof Error ? error.message : 'Failed to create paragraph';
              state.isLoading = false;
            });
          }
        },

        updateParagraph: async (id: string, updates: Partial<SavedParagraphMetadata>, content?: any) => {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          try {
            // If content is provided, recalculate stats
            let finalUpdates = { ...updates };
            if (content !== undefined) {
              const { wordCount, characterCount } = getContentStats(content);
              const contentPreview = generateContentPreview(content);
              finalUpdates = {
                ...finalUpdates,
                contentPreview,
                wordCount,
                characterCount
              };
            }

            const updatedParagraph = await window.fileStorage.updateParagraph(id, finalUpdates, content);

            set((state) => {
              const index = state.paragraphs.findIndex(p => p.id === id);
              if (index !== -1) {
                state.paragraphs[index] = updatedParagraph;
              }
              state.isLoading = false;
              state.currentView = 'list';
            });
          } catch (error) {
            set((state) => {
              state.error = error instanceof Error ? error.message : 'Failed to update paragraph';
              state.isLoading = false;
            });
          }
        },

        deleteParagraph: async (id: string) => {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          try {
            const success = await window.fileStorage.deleteParagraph(id);
            if (success) {
              set((state) => {
                state.paragraphs = state.paragraphs.filter(p => p.id !== id);
                if (state.selectedParagraphId === id) {
                  state.selectedParagraphId = null;
                }
                state.isLoading = false;
              });
            } else {
              throw new Error('Failed to delete paragraph');
            }
          } catch (error) {
            set((state) => {
              state.error = error instanceof Error ? error.message : 'Failed to delete paragraph';
              state.isLoading = false;
            });
          }
        },

        // Category actions
        createCategory: async (name: string, color?: string) => {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          try {
            const newCategory = await window.fileStorage.saveParagraphCategory({
              name,
              color
            });

            set((state) => {
              state.categories.push(newCategory);
              state.isLoading = false;
            });
          } catch (error) {
            set((state) => {
              state.error = error instanceof Error ? error.message : 'Failed to create category';
              state.isLoading = false;
            });
          }
        },

        updateCategory: async (id: string, updates: Partial<SavedParagraphCategory>) => {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          try {
            const updatedCategory = await window.fileStorage.updateParagraphCategory(id, updates);

            set((state) => {
              const index = state.categories.findIndex(c => c.id === id);
              if (index !== -1) {
                state.categories[index] = updatedCategory;
              }
              state.isLoading = false;
            });
          } catch (error) {
            set((state) => {
              state.error = error instanceof Error ? error.message : 'Failed to update category';
              state.isLoading = false;
            });
          }
        },

        deleteCategory: async (id: string) => {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          try {
            const success = await window.fileStorage.deleteParagraphCategory(id);
            if (success) {
              set((state) => {
                state.categories = state.categories.filter(c => c.id !== id);
                // Also update any paragraphs that were in this category
                state.paragraphs.forEach(paragraph => {
                  if (paragraph.categoryId === id) {
                    paragraph.categoryId = 'uncategorized';
                  }
                });
                if (state.selectedCategoryIds.includes(id)) {
                  state.selectedCategoryIds = state.selectedCategoryIds.filter(catId => catId !== id);
                }
                state.isLoading = false;
              });
            } else {
              throw new Error('Failed to delete category');
            }
          } catch (error) {
            set((state) => {
              state.error = error instanceof Error ? error.message : 'Failed to delete category';
              state.isLoading = false;
            });
          }
        },

        // UI actions
        openModal: () => {
          set((state) => {
            // Only open if not already open to prevent double opening
            if (!state.isModalOpen) {
              state.isModalOpen = true;
              // Don't reset currentView here - let it be set by the caller
              state.error = null;
            }
          });
        },

        // Atomic operation to open modal with specific view
        openModalWithView: (view: 'list' | 'create' | 'edit' | 'category-management' | 'update' | 'update-edit' | 'insert') => {
          set((state) => {
            // Only open if not already open to prevent double opening
            if (!state.isModalOpen) {
              state.isModalOpen = true;
              state.currentView = view;
              state.error = null;
            }
          });
        },

        closeModal: () => {
          set((state) => {
            state.isModalOpen = false;
            state.selectedParagraphId = null;
            state.selectedCategoryIds = [];
            state.searchQuery = '';
            state.currentView = 'list';
            state.initialCreateContent = null;
            state.error = null;
          });
        },

        setSelectedParagraph: (id: string | null) => {
          set((state) => {
            state.selectedParagraphId = id;
          });
        },

        setSelectedCategory: (ids: string[]) => {
          set((state) => {
            state.selectedCategoryIds = ids;
          });
        },

        setSearchQuery: (query: string) => {
          set((state) => {
            state.searchQuery = query;
          });
        },

        setCurrentView: (view: 'list' | 'create' | 'edit' | 'category-management' | 'update' | 'update-edit' | 'insert') => {
          set((state) => {
            state.currentView = view;
            state.error = null;
            // Always clear loading state when navigating to non-list views since they don't require data loading
            // This ensures that create/edit forms are always accessible even if initial data loading failed
            if (view !== 'list') {
              state.isLoading = false;
            }
          });
        },

        setInitialCreateContent: (content: any[] | null) => {
          set((state) => {
            state.initialCreateContent = content;
          });
        },

        clearError: () => {
          set((state) => {
            state.error = null;
          });
        },

        // Helper methods
        getParagraphsByCategory: (categoryId: string) => {
          const state = get();
          return state.paragraphs.filter(p => p.categoryId === categoryId);
        },

        getFilteredParagraphs: async () => {
          const state = get();
          let filtered = state.paragraphs;

          // Filter by selected categories
          if (state.selectedCategoryIds.length > 0) {
            const specialNoCategory = '__no_category__';
            const hasNoCategoryFilter = state.selectedCategoryIds.includes(specialNoCategory);

            filtered = filtered.filter(p => {
              // Handle special case for paragraphs with no category
              // Check for empty string, null, undefined, or falsy values
              if (hasNoCategoryFilter && (!p.categoryId || p.categoryId === '')) {
                return true;
              }

              // Handle normal category filtering
              if (state.selectedCategoryIds.includes(p.categoryId)) {
                return true;
              }

              return false;
            });
          }

          // Filter by search query
          if (state.searchQuery.trim()) {
            const query = state.searchQuery.toLowerCase();

            // First, filter by title, description, and tags (fast search)
            const quickFiltered = filtered.filter(p =>
              p.title.toLowerCase().includes(query) ||
              p.description?.toLowerCase().includes(query) ||
              p.contentPreview.toLowerCase().includes(query) ||
              p.tags?.some(tag => tag.toLowerCase().includes(query))
            );

            // If we have results from quick search, use them
            if (quickFiltered.length > 0) {
              filtered = quickFiltered;
            } else {
              // If no quick results, search full content with parallel loading
              const contentPromises = filtered.map(async (paragraph) => {
                let fullContent = state.searchContentCache.get(paragraph.id);

                if (!fullContent) {
                  try {
                    const result = await window.fileStorage.getSavedParagraphById(paragraph.id);
                    fullContent = result?.content ? extractFullTextContent(result.content) : '';
                    return { id: paragraph.id, content: fullContent, paragraph };
                  } catch (error) {
                    console.error(`Error loading content for paragraph ${paragraph.id}:`, error);
                    return { id: paragraph.id, content: paragraph.contentPreview, paragraph };
                  }
                }
                return { id: paragraph.id, content: fullContent, paragraph };
              });

              const contentResults = await Promise.all(contentPromises);

              // Single state update with all cache entries
              const newCacheEntries = new Map();
              const matchingParagraphs = [];

              contentResults.forEach(({ id, content, paragraph }) => {
                newCacheEntries.set(id, content);
                if (content.toLowerCase().includes(query)) {
                  matchingParagraphs.push(paragraph);
                }
              });

              // Batch update cache to prevent multiple re-renders
              set((state) => {
                newCacheEntries.forEach((content, id) => {
                  state.searchContentCache.set(id, content);
                });
              });

              filtered = matchingParagraphs;
            }
          }

          return [...filtered].sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
        },

        getCategoryById: (id: string) => {
          const state = get();
          return state.categories.find(c => c.id === id);
        },

        getParagraphById: (id: string) => {
          const state = get();
          return state.paragraphs.find(p => p.id === id);
        },

        generateContentPreview: (content: any) => {
          return generateContentPreview(content);
        },
      }))
    ),
    {
      name: 'saved-paragraphs-store',
    }
  )
);
