import React from 'react';
import { Label } from "../../ui/label";
import { Switch } from "../../ui/switch";
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '../../ui/select';
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from '../../ui/tooltip';
import { Button } from '../../ui/button';
import { Textarea } from '../../ui/textarea';
import { DocumentSettings, SystemPrompt, ProjectSettings as GlobalProjectSettings } from '../../../../types/global';
import { PromptManagementSectionProps } from './SettingsSidebarTypes';
import { useNotification } from '../../../contexts/NotificationContext';
import { ManagePromptsModal } from "./../manage-prompts-modal";
import { useSettingsSidebar } from './SettingsSidebarContext';

export function SettingsSidebarPrompts({
  activeFile,
  projectSettings,
  documentSettings,
  onUpdateProjectSettings,
  onSaveActiveDocumentSettings
}: PromptManagementSectionProps) {
  const { showNotification } = useNotification();
  const { isManagePromptsModalOpen, setIsManagePromptsModalOpen } = useSettingsSidebar();
  const [editedPromptContent, setEditedPromptContent] = React.useState<string | null>(null);
  const [promptContentDirty, setPromptContentDirty] = React.useState(false);
  const [isPromptTextareaInteractive, setIsPromptTextareaInteractive] = React.useState(false);

  const isManagePromptsModalOpenRef = React.useRef(isManagePromptsModalOpen);
  React.useEffect(() => {
    isManagePromptsModalOpenRef.current = isManagePromptsModalOpen;
  }, [isManagePromptsModalOpen]);

  // Memoized handlers for opening and closing the modal - context automatically manages UI extension state
  const openManagePromptsModal = React.useCallback((event?: React.MouseEvent) => {
    // Prevent event bubbling if called from a click event
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    // Prevent multiple rapid clicks
    if (isManagePromptsModalOpen) {
      return;
    }

    setIsManagePromptsModalOpen(true);
  }, [setIsManagePromptsModalOpen, isManagePromptsModalOpen]);

  const closeManagePromptsModal = React.useCallback(() => {
    setIsManagePromptsModalOpen(false);
  }, [setIsManagePromptsModalOpen]);

  // Note: UI extension state is now automatically managed by SettingsSidebarContext

  React.useEffect(() => {
    // console.log('[SettingsSidebarPrompts useEffect - ActiveFile/DocSettings Change] Triggered. ActiveFile:', !!activeFile, 'DocSettings:', !!documentSettings);
    if (activeFile && documentSettings) {
      const selectedGlobalPromptId = documentSettings.selectedSystemPromptId;
      // console.log('[SettingsSidebarPrompts useEffect] selectedGlobalPromptId:', selectedGlobalPromptId);

      if (documentSettings.systemPromptContentOverride !== undefined && documentSettings.systemPromptContentOverride !== '') {
        // console.log('[SettingsSidebarPrompts useEffect] Using systemPromptContentOverride:', documentSettings.systemPromptContentOverride?.substring(0, 50) + '...');
        setEditedPromptContent(documentSettings.systemPromptContentOverride);
        setPromptContentDirty(false);
      } else if (selectedGlobalPromptId) {
        // Only set content if we don't already have content (to prevent double setting)
        if (editedPromptContent === null || editedPromptContent === '') {
          // First check document prompts
          let foundPrompt = null;
          if (documentSettings.localSystemPrompts) {
            foundPrompt = documentSettings.localSystemPrompts.find((p: SystemPrompt) => p.id === selectedGlobalPromptId);
            if (foundPrompt) {
              // console.log('[SettingsSidebarPrompts useEffect] Found DOCUMENT prompt:', foundPrompt?.name, 'content length:', foundPrompt?.content?.length);
            }
          }

          // Then check global prompts if not found in document prompts
          if (!foundPrompt && projectSettings) {
            foundPrompt = projectSettings.systemPrompts.find((p: SystemPrompt) => p.id === selectedGlobalPromptId);
            if (foundPrompt) {
              // console.log('[SettingsSidebarPrompts useEffect] Found GLOBAL prompt:', foundPrompt?.name, 'content length:', foundPrompt?.content?.length);
            }
          }

          if (foundPrompt) {
            setEditedPromptContent(foundPrompt.content || '');
            setPromptContentDirty(false);
          }
        }
      } else {
        // console.log('[SettingsSidebarPrompts useEffect] Setting empty content');
        setEditedPromptContent('');
        setPromptContentDirty(false);
      }
    } else {
      // console.log('[SettingsSidebarPrompts useEffect] No activeFile or documentSettings, setting null');
      setEditedPromptContent(null);
      setPromptContentDirty(false);
    }
  }, [activeFile, documentSettings, projectSettings, editedPromptContent]);

  const handleToggleChange = async (key: keyof DocumentSettings, value: boolean) => {
    if (!activeFile || !documentSettings) {
      showNotification("No active document to update.", "error");
      return;
    }
    const newSettings = { ...documentSettings, [key]: value };
    try {
      await onSaveActiveDocumentSettings(newSettings);
      showNotification('Setting updated.', 'success');
    } catch (error) {
      showNotification('Failed to update setting.', 'error');
      console.error(`Error saving ${key}:`, error);
    }
  };

  const handleSelectChange = async (key: keyof DocumentSettings, value: string | null) => {
    // console.log('[SettingsSidebarPrompts handleSelectChange] Called with key:', key, 'value:', value);
    if (!activeFile || !documentSettings) {
      showNotification("No active document to update.", "error");
      return;
    }
    const newSettings = { ...documentSettings, [key]: value };
    if (key === 'selectedSystemPromptId') {
      newSettings.systemPromptContentOverride = undefined;

      // First check document prompts
      let selectedPrompt = null;
      if (documentSettings.localSystemPrompts && value) {
        selectedPrompt = documentSettings.localSystemPrompts.find((p: SystemPrompt) => p.id === value);
        if (selectedPrompt) {
          console.log('[SettingsSidebarPrompts handleSelectChange] Found selected DOCUMENT prompt:', selectedPrompt?.name, 'content length:', selectedPrompt?.content?.length);
        }
      }

      // Then check global prompts if not found in document prompts
      if (!selectedPrompt && projectSettings?.systemPrompts && value) {
        selectedPrompt = projectSettings.systemPrompts.find((p: SystemPrompt) => p.id === value);
        if (selectedPrompt) {
          // console.log('[SettingsSidebarPrompts handleSelectChange] Found selected GLOBAL prompt:', selectedPrompt?.name, 'content length:', selectedPrompt?.content?.length);
        }
      }

      // Set the content immediately for better UX
      const promptContent = selectedPrompt?.content || '';
      setEditedPromptContent(promptContent);
      setPromptContentDirty(false);
      // console.log('[SettingsSidebarPrompts handleSelectChange] Set editedPromptContent to:', promptContent.substring(0, 50) + '...');
    }
    try {
      await onSaveActiveDocumentSettings(newSettings);
      showNotification('Selection updated.', 'success');
    } catch (error) {
      showNotification('Failed to update selection.', 'error');
      console.error(`Error saving ${key}:`, error);
    }
  };

  const handleSavePromptOverride = async () => {
    if (!activeFile || !documentSettings || !promptContentDirty || editedPromptContent === null) {
      return;
    }

    const selectedPromptId = documentSettings.selectedSystemPromptId;
    if (!selectedPromptId) {
      showNotification('No prompt selected to update.', 'error');
      return;
    }

    // console.log('[SettingsSidebarPrompts] Saving prompt content for ID:', selectedPromptId);
    // console.log('[SettingsSidebarPrompts] New content:', editedPromptContent.substring(0, 100) + '...');

    try {
      // First check if it's a document prompt
      let isDocumentPrompt = false;
      let promptToUpdate = null;

      if (documentSettings.localSystemPrompts) {
        promptToUpdate = documentSettings.localSystemPrompts.find((p: SystemPrompt) => p.id === selectedPromptId);
        if (promptToUpdate) {
          isDocumentPrompt = true;
          console.log('[SettingsSidebarPrompts] Updating DOCUMENT prompt:', promptToUpdate.name);
        }
      }

      // If not found in document prompts, check global prompts
      if (!promptToUpdate && projectSettings?.systemPrompts) {
        promptToUpdate = projectSettings.systemPrompts.find((p: SystemPrompt) => p.id === selectedPromptId);
        if (promptToUpdate) {
          console.log('[SettingsSidebarPrompts] Updating GLOBAL prompt:', promptToUpdate.name);
        }
      }

      if (!promptToUpdate) {
        showNotification('Selected prompt not found.', 'error');
        return;
      }

      if (isDocumentPrompt) {
        // Update document prompt
        const updatedLocalPrompts = documentSettings.localSystemPrompts!.map((p: SystemPrompt) =>
          p.id === selectedPromptId ? { ...p, content: editedPromptContent } : p
        );

        const newSettings = {
          ...documentSettings,
          localSystemPrompts: updatedLocalPrompts,
          systemPromptContentOverride: undefined // Clear any override since we're updating the actual prompt
        };

        await onSaveActiveDocumentSettings(newSettings);
        showNotification(`Document prompt "${promptToUpdate.name}" updated successfully.`, 'success');
      } else {
        // Update global prompt
        const updatedGlobalPrompts = projectSettings!.systemPrompts.map((p: SystemPrompt) =>
          p.id === selectedPromptId ? { ...p, content: editedPromptContent } : p
        );

        const newProjectSettings = {
          ...projectSettings!,
          systemPrompts: updatedGlobalPrompts
        };

        await onUpdateProjectSettings(newProjectSettings);

        // Also clear any document override since we updated the source prompt
        const newSettings = {
          ...documentSettings,
          systemPromptContentOverride: undefined
        };
        await onSaveActiveDocumentSettings(newSettings);

        showNotification(`Global prompt "${promptToUpdate.name}" updated successfully.`, 'success');
      }

      setPromptContentDirty(false);
    } catch (error) {
      console.error('[SettingsSidebarPrompts] Error saving prompt:', error);
      showNotification('Failed to save prompt changes.', 'error');
    }
  };

  const handleCancelPromptOverride = () => {
    const selectedPromptId = documentSettings?.selectedSystemPromptId;
    if (documentSettings?.systemPromptContentOverride !== undefined) {
      setEditedPromptContent(documentSettings.systemPromptContentOverride);
    } else if (selectedPromptId) {
      // First check document prompts
      let foundPrompt = null;
      if (documentSettings?.localSystemPrompts) {
        foundPrompt = documentSettings.localSystemPrompts.find((p: SystemPrompt) => p.id === selectedPromptId);
      }

      // Then check global prompts if not found in document prompts
      if (!foundPrompt && projectSettings) {
        foundPrompt = projectSettings.systemPrompts.find((p: SystemPrompt) => p.id === selectedPromptId);
      }

      setEditedPromptContent(foundPrompt?.content || '');
    } else {
      setEditedPromptContent('');
    }
    setPromptContentDirty(false);
  };

  // Memoized callbacks for ManagePromptsModal
  const memoizedOnClose = React.useCallback(() => {
    console.log('[SettingsSidebarPrompts] ManagePromptsModal onClose triggered.');
    closeManagePromptsModal();
  }, [closeManagePromptsModal]);

  const memoizedOnUpdateProjectSettings = React.useCallback(async (updatedSettings: GlobalProjectSettings) => {
    console.log('[SettingsSidebarPrompts] ManagePromptsModal onUpdateProjectSettings triggered.');
    return onUpdateProjectSettings(updatedSettings);
  }, [onUpdateProjectSettings]);

  const memoizedOnUpdateActiveDocumentSettings = React.useCallback(async (updatedSettings: DocumentSettings) => {
    console.log('[SettingsSidebarPrompts] ManagePromptsModal onUpdateActiveDocumentSettings triggered.');
    return onSaveActiveDocumentSettings(updatedSettings);
  }, [onSaveActiveDocumentSettings]);

  const handleTextareaFocus = React.useCallback(() => {
    setIsPromptTextareaInteractive(true);
    // Note: UI extension state is automatically managed by context based on modal states
  }, [setIsPromptTextareaInteractive]);

  const handleTextareaBlur = React.useCallback(() => {
    setIsPromptTextareaInteractive(false);
    // Note: UI extension state is automatically managed by context based on modal states
  }, [setIsPromptTextareaInteractive]);



  const getVisiblePromptsForDropdown = React.useCallback((): SystemPrompt[] => {
    if (!projectSettings || !documentSettings || !documentSettings.visibleSystemPromptIds) {
      return [];
    }
    const visibleIds = new Set(documentSettings.visibleSystemPromptIds);
    return projectSettings.systemPrompts.filter((p: SystemPrompt) => visibleIds.has(p.id));
  }, [projectSettings, documentSettings]);

  const getAvailableDocumentPrompts = React.useCallback((): SystemPrompt[] => {
    if (!documentSettings?.localSystemPrompts) {
      return [];
    }
    const hasVisibleGlobalPrompts = getVisiblePromptsForDropdown().length > 0;
    return documentSettings.localSystemPrompts.filter((prompt: SystemPrompt) => {
      const isExplicitlyVisible = documentSettings.visibleSystemPromptIds?.includes(prompt.id);
      return isExplicitlyVisible || !hasVisibleGlobalPrompts;
    });
  }, [documentSettings, getVisiblePromptsForDropdown]);

  const hasAvailablePrompts = React.useCallback((): boolean => {
    return getVisiblePromptsForDropdown().length > 0 || getAvailableDocumentPrompts().length > 0;
  }, [getVisiblePromptsForDropdown, getAvailableDocumentPrompts]);

  return (
    <>
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h4 className="font-medium text-sm text-foreground">System Prompts</h4>
          <TooltipProvider delayDuration={300}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="link"
                  size="sm"
                  className="text-xs h-auto p-0 text-foreground"
                  onClick={openManagePromptsModal} // Use memoized handler
                >
                  Manage Prompts
                </Button>
              </TooltipTrigger>
              <TooltipContent side="left">
                <p>Open prompt management modal</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        <div className="flex items-center justify-between p-2 border border-foreground/50 hover:border-foreground/30 rounded-md">
          <Label htmlFor="prepend-prompt" className="text-xs cursor-pointer flex-grow pr-2 text-muted-foreground">
            Prepend to Copied Text
          </Label>
          <TooltipProvider delayDuration={300}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Switch
                  className="custom-switch"
                  id="prepend-prompt"
                  checked={documentSettings?.isSystemPromptPrepended || false}
                  onCheckedChange={(checked) => handleToggleChange('isSystemPromptPrepended', checked)}
                />
              </TooltipTrigger>
              <TooltipContent side="left">
                <p>Add prompt text at the beginning of copy operations</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        <TooltipProvider delayDuration={300}>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="relative w-full">
                <Select
                  value={documentSettings?.selectedSystemPromptId || 'none'}
                  onValueChange={(value) => handleSelectChange('selectedSystemPromptId', value === 'none' ? null : value)}
                  disabled={!hasAvailablePrompts() && !documentSettings?.selectedSystemPromptId}
                  // Note: UI extension state is automatically managed by context
                >
                  <SelectTrigger id="system-prompt-selector" className="dark:bg-slate-800 dark:border-slate-700 dark:text-slate-300">
                    <SelectValue placeholder="Select a system prompt for this document" />
                  </SelectTrigger>
                  <SelectContent className="dark:bg-slate-800 dark:border-slate-700">
                    <SelectItem value="none" className="dark:text-slate-300 dark:focus:bg-slate-700">None</SelectItem>
                    {/* Global Prompts Section */}
                    <SelectGroup>
                      <SelectLabel className="text-xs font-semibold text-muted-foreground dark:text-slate-400">Global Prompts</SelectLabel>
                      {getVisiblePromptsForDropdown()
                        .filter(prompt => !prompt.parentPromptId)
                        .map((prompt) => (
                          <SelectItem key={prompt.id} value={prompt.id} className="dark:text-slate-300 dark:focus:bg-slate-700">
                            {prompt.name}
                          </SelectItem>
                        ))}
                    </SelectGroup>
                    {/* Document Prompts Section */}
                    {getAvailableDocumentPrompts().length > 0 && (
                      <SelectGroup>
                        <SelectLabel className="text-xs font-semibold text-muted-foreground dark:text-slate-400 mt-2">Document Prompts</SelectLabel>
                        {getAvailableDocumentPrompts().map((prompt: SystemPrompt) => (
                          <SelectItem key={prompt.id} value={prompt.id} className="dark:text-slate-300 dark:focus:bg-slate-700">
                            {prompt.name}
                          </SelectItem>
                        ))}
                      </SelectGroup>
                    )}
                  </SelectContent>
                </Select>
              </div>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>Select a prompt to use</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <div className="relative">
          <Textarea
            value={editedPromptContent ?? ''}
            onChange={(e) => {
              setEditedPromptContent(e.target.value);
              setPromptContentDirty(true);
            }}
            onFocus={handleTextareaFocus}
            onBlur={handleTextareaBlur}
            placeholder={activeFile && documentSettings ? "System prompt content (override or new)" : "Open a document to edit its system prompt"}
            className="w-full min-h-[180px] dark:bg-slate-800 dark:border-slate-700 dark:text-slate-200 dark:placeholder-slate-400 prompt-content-scrollbar"
            disabled={!activeFile || !documentSettings || (!isPromptTextareaInteractive && !promptContentDirty && editedPromptContent === null)}
          />
        </div>

        {promptContentDirty && activeFile && documentSettings && (
          <div className="flex justify-end gap-2 pt-1">
            <TooltipProvider delayDuration={300}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="sm" onClick={handleCancelPromptOverride}>Cancel</Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Discard changes</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <TooltipProvider delayDuration={300}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button size="sm" onClick={handleSavePromptOverride}>Save Changes</Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Save changes to the selected prompt</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        )}
      </div>

      {/* Manage Prompts Modal */}
      <ManagePromptsModal
        isOpen={isManagePromptsModalOpen}
        onClose={closeManagePromptsModal} // Use memoized handler
      />
    </>
  );
}
