import React, { useEffect } from 'react';
import { ChevronLeftIcon } from 'lucide-react';
import { AnimatePresence, motion } from 'framer-motion';
import { cn } from './../../../lib/utils';
import { useSettingsSidebar } from './SettingsSidebarContext';
import { SettingsSidebarHeader } from './SettingsSidebarHeader';
import { SettingsSidebarContent } from './SettingsSidebarContent';
import { SettingsSidebarProps } from './SettingsSidebarTypes';
import { useDocumentStore, useActiveDocument, useProjectSettings } from '../../../stores/blockDocumentStore';
import { useSidebarContext } from '../../../contexts/SidebarContext';

export function SettingsSidebar(_props: SettingsSidebarProps) {
  // Use selectors for optimized renders
  const projectSettings = useProjectSettings();
  const activeFile = useActiveDocument();
  const { saveProjectSettings, saveDocumentMetadata } = useDocumentStore();
  const { setRightSidebarExpanded } = useSidebarContext();

  useEffect(() => {
    if (projectSettings) {
      console.log('[SettingsSidebar] Project settings loaded/updated:', projectSettings);
    }
  }, [projectSettings]);

  useEffect(() => {
    if (activeFile?.documentSettings) {
      console.log('[SettingsSidebar] Document settings for active file loaded/updated:', activeFile.documentSettings);
    }
  }, [activeFile?.documentSettings]);

  // Define the handlers using the store methods with React.useCallback for memoization
  const onUpdateProjectSettings = React.useCallback(async (updatedSettings: any) => {
    try {
      await saveProjectSettings(updatedSettings);
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  }, [saveProjectSettings]);

  const onSaveActiveDocumentSettings = React.useCallback(async (updatedDocSettings: any) => {
    if (!activeFile || !activeFile.id) {
      console.error('[SettingsSidebar] onSaveActiveDocumentSettings: No active document or ID.');
      return Promise.reject(new Error('No active document'));
    }
    try {
      await saveDocumentMetadata(activeFile.id, updatedDocSettings);
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  }, [activeFile, saveDocumentMetadata]);

  const {
    expanded,
    toggleExpanded,
    isPinned,
    togglePinned,
    isUiExtensionOpen,
    setIsUiExtensionOpen
  } = useSettingsSidebar();

  const [leaveTimeoutId, setLeaveTimeoutId] = React.useState<NodeJS.Timeout | null>(null);

  const documentSettings = activeFile?.documentSettings;

  // Sync sidebar expanded state with context
  useEffect(() => {
    setRightSidebarExpanded(expanded);
  }, [expanded, setRightSidebarExpanded]);

  const handleMouseEnterSidebar = () => {
    if (leaveTimeoutId) {
      clearTimeout(leaveTimeoutId);
      setLeaveTimeoutId(null);
    }
  };

  const handleHoverStripEnter = () => {
    handleMouseEnterSidebar();
    if (!isPinned && !expanded) {
      toggleExpanded();
    }
  };

  const handleSidebarLeave = () => {
    // Don't start timeout if UI extension (modal/dropdown) is open
    if (isUiExtensionOpen) {
      return;
    }
    if (leaveTimeoutId) clearTimeout(leaveTimeoutId);
    const newTimeoutId = setTimeout(() => {
      // Double-check that UI extension is still not open before closing
      if (!isPinned && expanded && !isUiExtensionOpen) {
        toggleExpanded();
      }
      setLeaveTimeoutId(null); // Clear the timeout ID after execution
    }, 300);
    setLeaveTimeoutId(newTimeoutId);
  };

  React.useEffect(() => {
    if (isPinned && !expanded) {
      toggleExpanded();
    }
  }, [isPinned, expanded]);

  // Cleanup timeout on unmount
  React.useEffect(() => {
    return () => {
      if (leaveTimeoutId) {
        clearTimeout(leaveTimeoutId);
      }
    };
  }, [leaveTimeoutId]);

  const hoverStripVisibilityClass = !expanded && !isPinned ? "opacity-100" : "opacity-0 pointer-events-none";

  if (!expanded) {
    return (
      <div className="absolute right-0 z-30" style={{
        top: 'calc(30px + 2 * (100vh - 30px) / 3)',
        height: 'calc((100vh - 30px) / 3)'
      }}>
        <div
          className={cn(
            "w-8 h-full bg-background border-l-2 border-t-2 border-border flex items-center justify-center cursor-pointer hover:bg-muted transition-colors",
            hoverStripVisibilityClass
          )}
          onMouseEnter={handleHoverStripEnter}
          onClick={toggleExpanded}
          title="Expand Settings"
        >
          <ChevronLeftIcon className="h-4 w-4 text-primary" />
        </div>
      </div>
    );
  }

  return (
    <AnimatePresence>
      {expanded && (
        <motion.div
          initial={{ width: '0%', opacity: 0 }}
          animate={{ width: '25rem', opacity: 1 }}
          exit={{ width: '0%', opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="fixed right-0 z-40 flex flex-col bg-background border-l border-t border-border shadow-lg"
          style={{
            top: '30px', // Full height when expanded
            height: 'calc(100vh - 30px)', // Full height when expanded
            pointerEvents: 'auto'
          }}
          onMouseEnter={handleMouseEnterSidebar}
          onMouseLeave={handleSidebarLeave}
        >
          <SettingsSidebarHeader
            isPinned={isPinned}
            togglePinned={togglePinned}
            toggleExpanded={toggleExpanded}
          />

          <SettingsSidebarContent
            activeFile={activeFile}
            projectSettings={projectSettings}
            documentSettings={documentSettings}
            onUpdateProjectSettings={onUpdateProjectSettings}
            onSaveActiveDocumentSettings={onSaveActiveDocumentSettings}
            isUiExtensionOpen={isUiExtensionOpen}
            setIsUiExtensionOpen={setIsUiExtensionOpen}
          />
        </motion.div>
      )}
    </AnimatePresence>
  );
}
