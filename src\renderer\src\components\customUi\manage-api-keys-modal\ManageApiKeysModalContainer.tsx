import React, { useCallback, memo } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { PlusCircleIcon } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from "../../ui/dialog";
import { But<PERSON> } from "../../ui/button";
import { Input } from "../../ui/input";
import { ScrollArea } from '../../ui/scroll-area';

// Components
import { ApiKeyItem } from './components/ApiKeyItem';
import { DeleteConfirmDialog } from './components/DeleteConfirmDialog';

// Hooks
import { useApiKeyManagement } from './hooks/useApiKeyManagement';

// Types
import { ManageApiKeysModalProps } from './types';

const ManageApiKeysModalComponent: React.FC<ManageApiKeysModalProps> = ({
  isOpen,
  onClose,
  projectSettings,
  onUpdateProjectSettings
}) => {
  // Main API key management hook
  const {
    editedConfigs,
    newConfigName,
    setNewConfigName,
    configToDelete,
    isDeleteConfirmOpen,
    setIsDeleteConfirmOpen,
    handleUpdateConfigField,
    handleCreateNewConfig,
    handleVisibilityToggle,
    handleInitiateDelete,
    handleDeleteConfig,
    moveConfig,
    saveAllPendingChanges,
    saveAllPendingChangesRef
  } = useApiKeyManagement({
    isOpen,
    projectSettings,
    onUpdateProjectSettings
  });

  // Handle modal close
  const handleClose = useCallback(async () => {
    await saveAllPendingChangesRef.current();
    onClose();
  }, [onClose, saveAllPendingChangesRef]);

  // Handle delete confirmation close
  const handleDeleteConfirmClose = useCallback(() => {
    setIsDeleteConfirmOpen(false);
  }, [setIsDeleteConfirmOpen]);

  return (
    <DndProvider backend={HTML5Backend}>
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="max-w-4xl h-[80vh] flex flex-col p-0 gap-0">
          <DialogHeader className="p-4 border-b">
            <DialogTitle className='text-foreground'>Manage API Keys</DialogTitle>
            <DialogDescription>
              Manage your API configurations for various services. Changes are saved automatically.
            </DialogDescription>
          </DialogHeader>

          <div className="flex-grow overflow-y-auto p-4 m-0">
            {/* Add New Configuration Section */}
            <div className="mb-4 flex gap-2 items-center">
              <Input
                type="text"
                placeholder="New API configuration name..."
                value={newConfigName}
                onChange={(e) => setNewConfigName(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleCreateNewConfig()}
                className="flex-grow max-w-md text-text-foreground"
              />
              <Button onClick={handleCreateNewConfig} variant="outline" size="sm">
                <PlusCircleIcon className="mr-2 h-4 w-4" /> Create Configuration
              </Button>
            </div>

            {editedConfigs.length > 0 ? (
              <ScrollArea className="h-[calc(100%-60px)] pr-3">
                {editedConfigs.map((config, index) => (
                  <ApiKeyItem
                    key={config.id}
                    config={config}
                    index={index}
                    moveConfig={moveConfig}
                    handleVisibilityToggle={handleVisibilityToggle}
                    handleInitiateDelete={handleInitiateDelete}
                    handleUpdateConfigField={handleUpdateConfigField}
                  />
                ))}
              </ScrollArea>
            ) : (
              <p className="text-muted-foreground text-sm text-center py-8">
                No API configurations yet. Create one above.
              </p>
            )}
          </div>

          <DialogFooter className="p-4 flex justify-between items-center">
            <p className="text-sm text-muted-foreground italic">All changes are automagically saved.</p>
            <Button variant="outline" onClick={handleClose}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmDialog
        isOpen={isDeleteConfirmOpen}
        onClose={handleDeleteConfirmClose}
        onConfirm={handleDeleteConfig}
        configName={configToDelete?.name || ''}
      />
    </DndProvider>
  );
};

// Apply React.memo and export
export const ManageApiKeysModalContainer = memo(ManageApiKeysModalComponent, (prevProps, nextProps) => {
  // Custom comparison function for React.memo
  const isEqual = prevProps.isOpen === nextProps.isOpen &&
                  prevProps.projectSettings === nextProps.projectSettings && // Added projectSettings
                  prevProps.onClose === nextProps.onClose &&
                  prevProps.onUpdateProjectSettings === nextProps.onUpdateProjectSettings; // Added onUpdateProjectSettings

  if (!isEqual) {
    console.log('[ManageApiKeysModal memo] Props are NOT equal, re-rendering. Prev:', prevProps, 'Next:', nextProps);
  }
  return isEqual;
});
