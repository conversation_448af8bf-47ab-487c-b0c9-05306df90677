import { useCallback } from 'react';
import { useSavedParagraphsStore } from '../../../../stores/savedParagraphsStore';
import { SavedParagraphCategory } from '../../../../../types/global';
import { useNotification } from '../../../../contexts/NotificationContext';

/**
 * Hook for managing saved paragraph categories
 * Provides convenient methods for CRUD operations on categories
 */
export const useCategoryManagement = () => {
  const {
    categories,
    paragraphs,
    isLoading,
    error,
    selectedCategoryIds,

    // Actions
    loadCategories,
    createCategory,
    updateCategory,
    deleteCategory,
    setSelectedCategory,
    clearError,

    // Helper methods
    getParagraphsByCategory
  } = useSavedParagraphsStore();

  const { showNotification } = useNotification();

  // Create a new category with validation
  const createCategorySafe = useCallback(async (name: string, color?: string) => {
    if (!name.trim()) {
      showNotification('Category name is required', 'error');
      return false;
    }

    // Check for duplicate names
    const existingCategory = categories.find(
      c => c.name.toLowerCase() === name.trim().toLowerCase()
    );

    if (existingCategory) {
      showNotification('A category with this name already exists', 'error');
      return false;
    }

    try {
      await createCategory(name.trim(), color);
      showNotification(`Category "${name}" created successfully`, 'success');
      return true;
    } catch (error) {
      console.error('Failed to create category:', error);
      showNotification('Failed to create category', 'error');
      return false;
    }
  }, [createCategory, categories, showNotification]);

  // Update a category with validation
  const updateCategorySafe = useCallback(async (
    id: string,
    updates: Partial<SavedParagraphCategory>
  ) => {
    if (updates.name && !updates.name.trim()) {
      showNotification('Category name cannot be empty', 'error');
      return false;
    }

    // Check for duplicate names (excluding current category)
    if (updates.name) {
      const existingCategory = categories.find(
        c => c.id !== id && c.name.toLowerCase() === updates.name!.trim().toLowerCase()
      );

      if (existingCategory) {
        showNotification('A category with this name already exists', 'error');
        return false;
      }
    }

    try {
      const updatedData = { ...updates };
      if (updatedData.name) {
        updatedData.name = updatedData.name.trim();
      }

      await updateCategory(id, updatedData);
      showNotification('Category updated successfully', 'success');
      return true;
    } catch (error) {
      console.error('Failed to update category:', error);
      showNotification('Failed to update category', 'error');
      return false;
    }
  }, [updateCategory, categories, showNotification]);

  // Delete a category with validation
  const deleteCategorySafe = useCallback(async (id: string, name: string) => {
    // Check if category is in use
    const paragraphsInCategory = getParagraphsByCategory(id);

    if (paragraphsInCategory.length > 0) {
      showNotification(
        `Cannot delete category "${name}" because it contains ${paragraphsInCategory.length} paragraph(s). Please move or delete the paragraphs first.`,
        'error'
      );
      return false;
    }

    // Prevent deletion of default "General" category
    if (name.toLowerCase() === 'general') {
      showNotification('Cannot delete the default "General" category', 'error');
      return false;
    }

    try {
      await deleteCategory(id);
      showNotification(`Category "${name}" deleted successfully`, 'success');

      // Clear selection if the deleted category was selected
      if (selectedCategoryIds.includes(id)) {
        setSelectedCategory(selectedCategoryIds.filter(catId => catId !== id));
      }

      return true;
    } catch (error) {
      console.error('Failed to delete category:', error);
      showNotification('Failed to delete category', 'error');
      return false;
    }
  }, [deleteCategory, getParagraphsByCategory, selectedCategoryIds, setSelectedCategory, showNotification]);

  // Get category by ID
  const getCategoryById = useCallback((id: string) => {
    return categories.find(c => c.id === id) || null;
  }, [categories]);

  // Get category usage statistics
  const getCategoryStats = useCallback((categoryId: string) => {
    const paragraphsInCategory = getParagraphsByCategory(categoryId);
    return {
      paragraphCount: paragraphsInCategory.length,
      lastUsed: paragraphsInCategory.length > 0
        ? Math.max(...paragraphsInCategory.map(p => new Date(p.updatedAt).getTime()))
        : null
    };
  }, [getParagraphsByCategory]);

  // Ensure default "General" category exists
  const ensureDefaultCategory = useCallback(async () => {
    const generalCategory = categories.find(c => c.name.toLowerCase() === 'general');

    if (!generalCategory) {
      try {
        await createCategory('General', '#A2C4C9'); // Dusty teal pastel color
        console.log('Created default "General" category');
      } catch (error) {
        console.error('Failed to create default category:', error);
      }
    }
  }, [categories, createCategory]);

  // Get category options for dropdowns (excluding specified IDs)
  const getCategoryOptions = useCallback((excludeIds: string[] = []) => {
    return categories
      .filter(c => !excludeIds.includes(c.id))
      .map(c => ({ value: c.id, label: c.name, color: c.color }));
  }, [categories]);

  // Get the default category (General)
  const getDefaultCategory = useCallback(() => {
    return categories.find(c => c.name.toLowerCase() === 'general') || categories[0] || null;
  }, [categories]);

  return {
    // State
    categories,
    isLoading,
    error,
    selectedCategoryIds,

    // Computed data
    selectedCategories: selectedCategoryIds.map(id => getCategoryById(id)).filter(Boolean),
    defaultCategory: getDefaultCategory(),
    categoryOptions: getCategoryOptions(),

    // Actions
    createCategory: createCategorySafe,
    updateCategory: updateCategorySafe,
    deleteCategory: deleteCategorySafe,
    setSelectedCategory,
    clearError,

    // Utilities
    getCategoryById,
    getCategoryStats,
    getCategoryOptions,
    ensureDefaultCategory,

    // Raw store methods
    rawStore: {
      loadCategories,
      getParagraphsByCategory
    }
  };
};
